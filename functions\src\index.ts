// Firebase Functions for Hive Campus
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import { createHash } from 'crypto';
import cors from 'cors';

// Initialize Firebase Admin
admin.initializeApp();

// CORS configuration for development and production
const corsHandler = cors({
  origin: [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://localhost:5000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});

// Simple test function
export const testFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Test function working',
      timestamp: new Date().toISOString()
    });
  });

// Function to set admin PIN
export const setAdminPin = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify the user is authenticated and is an admin
      if (!context.auth) {
        throw new functions.https.HttpsError(
          'unauthenticated',
          'User must be authenticated'
        );
      }

      const { pin } = data;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'PIN must be exactly 8 digits'
        );
      }

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Only admin users can set PIN'
        );
      }

      // Hash the PIN for security
      const hashedPin = createHash('sha256').update(pin).digest('hex');

      // Store the hashed PIN in admin settings
      await admin.firestore().collection('adminSettings').doc('security').set({
        adminPin: hashedPin,
        pinSetAt: admin.firestore.Timestamp.now(),
        pinSetBy: context.auth.uid
      }, { merge: true });

      console.log(`Admin PIN set by user: ${context.auth.uid}`);

      return {
        success: true,
        message: 'Admin PIN set successfully'
      };

    } catch (error) {
      console.error('Error setting admin PIN:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to set admin PIN',
        error
      );
    }
  });

// Function to verify admin PIN
export const verifyAdminPin = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify the user is authenticated and is an admin
      if (!context.auth) {
        throw new functions.https.HttpsError(
          'unauthenticated',
          'User must be authenticated'
        );
      }

      const { pin } = data;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'PIN must be exactly 8 digits'
        );
      }

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Only admin users can verify PIN'
        );
      }

      // Get stored PIN hash
      const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
      if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
        throw new functions.https.HttpsError(
          'not-found',
          'Admin PIN not set. Please set up your PIN first.'
        );
      }

      // Hash the provided PIN and compare
      const hashedPin = createHash('sha256').update(pin).digest('hex');
      const storedPin = securityDoc.data()?.adminPin;

      if (hashedPin !== storedPin) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Invalid PIN'
        );
      }

      // Update last access time
      await admin.firestore().collection('users').doc(context.auth.uid).update({
        lastAdminAccess: admin.firestore.Timestamp.now()
      });

      console.log(`Admin PIN verified for user: ${context.auth.uid}`);

      return {
        success: true,
        message: 'PIN verified successfully'
      };

    } catch (error) {
      console.error('Error verifying admin PIN:', error);

      // If it's already a Firebase error, re-throw it as-is
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }

      // Otherwise, wrap it as an internal error
      throw new functions.https.HttpsError(
        'internal',
        'Failed to verify admin PIN',
        error
      );
    }
  });

// Function to fix existing admin user (for setup)
export const fixAdminUser = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, _context) => {
    try {
      const { email } = data;

      if (!email) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Email is required'
        );
      }

      // Get user by email
      const userRecord = await admin.auth().getUserByEmail(email);

      // Set custom claims with both admin and role
      await admin.auth().setCustomUserClaims(userRecord.uid, {
        admin: true,
        role: 'admin'
      });

      // Update user profile in Firestore
      await admin.firestore().collection('users').doc(userRecord.uid).set({
        role: 'admin',
        updatedAt: admin.firestore.Timestamp.now(),
        status: 'active',
        adminLevel: 'super',
        permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
      }, { merge: true });

      console.log(`Admin user fixed: ${email} (${userRecord.uid})`);

      return {
        success: true,
        message: 'Admin user configured successfully',
        uid: userRecord.uid
      };

    } catch (error) {
      console.error('Error fixing admin user:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to fix admin user',
        error
      );
    }
  });

// Get Stripe Connect account status
export const getStripeConnectAccountStatus = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching Stripe Connect account status for user:', userId);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        console.log(`No connect account found for user: ${userId}`);
        return null;
      }

      const connectAccount = connectAccountDoc.data();
      console.log('Connect account data:', connectAccount);

      return {
        accountId: connectAccount?.stripeAccountId || null,
        onboardingUrl: connectAccount?.onboardingUrl || null,
        dashboardUrl: connectAccount?.dashboardUrl || null,
        isOnboarded: connectAccount?.isOnboarded || false,
        chargesEnabled: connectAccount?.chargesEnabled || false,
        payoutsEnabled: connectAccount?.payoutsEnabled || false,
      };
    } catch (error) {
      console.error('Error in getStripeConnectAccountStatus:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get pending payouts for seller
export const getSellerPendingPayouts = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching pending payouts for user:', userId);

      // Query orders where the user is the seller and funds haven't been released
      const ordersQuery = admin.firestore()
        .collection('orders')
        .where('sellerId', '==', userId)
        .where('status', '==', 'payment_succeeded')
        .where('fundsReleased', '==', false)
        .orderBy('createdAt', 'desc')
        .limit(50);

      const ordersSnapshot = await ordersQuery.get();
      const pendingPayouts = ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log(`Found ${pendingPayouts.length} pending payouts for user: ${userId}`);
      return pendingPayouts;
    } catch (error) {
      console.error('Error in getSellerPendingPayouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Create Stripe Connect account
export const createStripeConnectAccount = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { accountType } = data;
      const userId = context.auth.uid;

      if (!accountType || !['student', 'merchant'].includes(accountType)) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
      }

      console.log(`Creating Stripe Connect account for user: ${userId}, type: ${accountType}`);

      // Check if user already has a Connect account
      const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
      if (existingAccountDoc.exists) {
        const existingAccount = existingAccountDoc.data();
        console.log('User already has a Connect account:', existingAccount);

        return {
          accountId: existingAccount?.stripeAccountId,
          onboardingUrl: existingAccount?.onboardingUrl || `https://hivecampus.app/settings/payment?existing=true`
        };
      }

      // For now, create a mock account since we don't have Stripe configured
      const mockAccountId = `acct_mock_${userId.substring(0, 8)}`;
      const mockConnectAccount = {
        userId: userId,
        stripeAccountId: mockAccountId,
        accountType: accountType,
        isOnboarded: false,
        chargesEnabled: false,
        payoutsEnabled: false,
        detailsSubmitted: false,
        onboardingUrl: `https://hivecampus.app/settings/payment?mock=true&message=Stripe Connect setup will be available soon`,
        isMockAccount: true,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      };

      await admin.firestore().collection('connectAccounts').doc(userId).set(mockConnectAccount);

      console.log(`Mock Stripe Connect account created for user: ${userId}`);

      return {
        accountId: mockAccountId,
        onboardingUrl: mockConnectAccount.onboardingUrl,
      };
    } catch (error) {
      console.error('Error in createStripeConnectAccount:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get Stripe Connect onboarding link
export const getStripeConnectOnboardingLink = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log(`Getting onboarding link for user: ${userId}`);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'No Stripe account found for user');
      }

      const connectAccount = connectAccountDoc.data();

      if (connectAccount?.isOnboarded) {
        throw new functions.https.HttpsError('failed-precondition', 'Account is already fully onboarded');
      }

      // Return the existing onboarding URL or create a new mock one
      const onboardingUrl = connectAccount?.onboardingUrl ||
        `https://hivecampus.app/settings/payment?mock=true&message=Stripe Connect setup will be available soon`;

      return {
        onboardingUrl: onboardingUrl
      };
    } catch (error) {
      console.error('Error in getStripeConnectOnboardingLink:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get wallet data for a user
export const getWalletData = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Getting wallet data for user:', userId);

      // Get wallet from Firestore
      const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

      if (!walletDoc.exists) {
        // Initialize wallet if it doesn't exist
        const referralCode = `user${userId.substring(0, 6)}`;
        const walletData = {
          userId,
          balance: 0,
          referralCode,
          usedReferral: false,
          history: [],
          grantedBy: 'system',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        };

        await admin.firestore().collection('wallets').doc(userId).set(walletData);
        return walletData;
      }

      return walletDoc.data();
    } catch (error) {
      console.error('Error in getWalletData:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get wallet settings (admin only)
export const getWalletSettings = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      // Verify admin role
      const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      console.log('Getting wallet settings for admin:', context.auth.uid);

      // Get wallet settings from admin configuration
      const settingsDoc = await admin.firestore().collection('adminSettings').doc('walletConfig').get();

      let settings = {
        signupBonus: 0,
        referralBonus: 0,
        enableSignupBonus: false,
        enableReferralBonus: false
      };

      if (settingsDoc.exists) {
        const data = settingsDoc.data();
        settings = {
          signupBonus: data?.signupBonus || 0,
          referralBonus: data?.referralBonus || 0,
          enableSignupBonus: data?.enableSignupBonus || false,
          enableReferralBonus: data?.enableReferralBonus || false
        };
      }

      return {
        success: true,
        settings
      };
    } catch (error) {
      console.error('Error in getWalletSettings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Configure wallet settings (admin only, callable function)
export const configureWalletSettingsCallable = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      // Verify admin role
      const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      // Get settings from data
      const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = data;

      // Validate settings
      if (typeof signupBonus !== 'number' || typeof referralBonus !== 'number' ||
          typeof enableSignupBonus !== 'boolean' || typeof enableReferralBonus !== 'boolean') {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid settings format');
      }

      // Save settings
      await admin.firestore().collection('adminSettings').doc('walletConfig').set({
        signupBonus,
        referralBonus,
        enableSignupBonus,
        enableReferralBonus,
        updatedAt: admin.firestore.Timestamp.now(),
        updatedBy: context.auth.uid
      });

      console.log(`Wallet settings updated by admin: ${context.auth.uid}`);

      return {
        success: true,
        message: 'Wallet settings updated successfully'
      };
    } catch (error) {
      console.error('Error configuring wallet settings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Grant wallet credit to a user (admin only)
export const grantWalletCredit = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      // Verify admin role
      const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      const { userId, amount, description } = data;

      if (!userId || !amount || amount <= 0) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid user ID and positive amount are required');
      }

      // Check if target user exists
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }

      console.log(`Admin ${context.auth.uid} granting $${amount} to user ${userId}`);

      // Create transaction record
      const transaction = {
        id: admin.firestore().collection('temp').doc().id,
        userId: userId,
        type: 'admin_grant',
        amount: parseFloat(amount.toFixed(2)),
        description: description || `Admin credit grant`,
        timestamp: admin.firestore.Timestamp.now(),
        grantedBy: context.auth.uid,
        source: 'admin_grant',
        createdAt: admin.firestore.Timestamp.now()
      };

      // Get or create wallet
      const walletRef = admin.firestore().collection('wallets').doc(userId);
      const walletDoc = await walletRef.get();

      if (!walletDoc.exists) {
        // Create new wallet
        const referralCode = `user${userId.substring(0, 6)}`;
        await walletRef.set({
          userId,
          balance: transaction.amount,
          referralCode,
          usedReferral: false,
          history: [transaction],
          grantedBy: 'admin',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        });
      } else {
        // Update existing wallet
        await walletRef.update({
          balance: admin.firestore.FieldValue.increment(transaction.amount),
          history: admin.firestore.FieldValue.arrayUnion(transaction),
          lastUpdated: admin.firestore.Timestamp.now()
        });
      }

      return {
        success: true,
        message: `Successfully granted $${transaction.amount} to user`,
        transaction
      };
    } catch (error) {
      console.error('Error in grantWalletCredit:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Process referral code and grant bonuses
export const processReferralCode = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { referralCode } = data;
      const userId = context.auth.uid;

      if (!referralCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Referral code is required');
      }

      console.log(`Processing referral code ${referralCode} for user ${userId}`);

      // Get wallet settings to check if referral bonuses are enabled
      const settingsDoc = await admin.firestore().collection('adminSettings').doc('walletConfig').get();
      const settings = settingsDoc.exists ? settingsDoc.data() : {
        enableReferralBonus: false,
        referralBonus: 5
      };

      if (!settings?.enableReferralBonus) {
        throw new functions.https.HttpsError('failed-precondition', 'Referral bonuses are currently disabled');
      }

      const referralBonus = settings?.referralBonus || 5;

      // Check if referral code exists
      const referralDoc = await admin.firestore().collection('referralCodes').doc(referralCode).get();
      if (!referralDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Invalid referral code');
      }

      const referralData = referralDoc.data();

      if (!referralData) {
        throw new functions.https.HttpsError('not-found', 'Invalid referral code data');
      }

      // Check if user is trying to use their own referral code
      if (referralData.userId === userId) {
        throw new functions.https.HttpsError('invalid-argument', 'Cannot use your own referral code');
      }

      // Check if user has already used a referral code
      const userWalletDoc = await admin.firestore().collection('wallets').doc(userId).get();
      if (userWalletDoc.exists && userWalletDoc.data()?.usedReferral) {
        throw new functions.https.HttpsError('failed-precondition', 'You have already used a referral code');
      }

      // Check if this user has already been referred by this code
      if (referralData.usedBy?.includes(userId)) {
        throw new functions.https.HttpsError('failed-precondition', 'You have already used this referral code');
      }

      // Create transactions for both users
      const now = admin.firestore.Timestamp.now();

      const newUserTransaction = {
        id: admin.firestore().collection('temp').doc().id,
        userId: userId,
        type: 'referral_bonus',
        amount: referralBonus,
        description: `Referral bonus for using code ${referralCode}`,
        timestamp: now,
        source: 'referral_bonus',
        createdAt: now
      };

      const referrerTransaction = {
        id: admin.firestore().collection('temp').doc().id,
        userId: referralData.userId,
        type: 'referral_bonus',
        amount: referralBonus,
        description: `Referral bonus for referring new user`,
        timestamp: now,
        source: 'referral_bonus',
        createdAt: now
      };

      // Use batch to ensure atomicity
      const batch = admin.firestore().batch();

      // Update new user's wallet
      const newUserWalletRef = admin.firestore().collection('wallets').doc(userId);
      if (userWalletDoc.exists) {
        batch.update(newUserWalletRef, {
          balance: admin.firestore.FieldValue.increment(referralBonus),
          usedReferral: true,
          history: admin.firestore.FieldValue.arrayUnion(newUserTransaction),
          lastUpdated: now
        });
      } else {
        // Create new wallet
        const referralCodeForUser = `user${userId.substring(0, 6)}`;
        batch.set(newUserWalletRef, {
          userId,
          balance: referralBonus,
          referralCode: referralCodeForUser,
          usedReferral: true,
          history: [newUserTransaction],
          grantedBy: 'referral',
          createdAt: now,
          lastUpdated: now
        });
      }

      // Update referrer's wallet
      const referrerWalletRef = admin.firestore().collection('wallets').doc(referralData.userId);
      batch.update(referrerWalletRef, {
        balance: admin.firestore.FieldValue.increment(referralBonus),
        history: admin.firestore.FieldValue.arrayUnion(referrerTransaction),
        lastUpdated: now
      });

      // Update referral code usage
      const referralCodeRef = admin.firestore().collection('referralCodes').doc(referralCode);
      batch.update(referralCodeRef, {
        usedBy: admin.firestore.FieldValue.arrayUnion(userId),
        totalRewards: admin.firestore.FieldValue.increment(referralBonus * 2), // Both users get bonus
        updatedAt: now
      });

      await batch.commit();

      return {
        success: true,
        message: `Referral bonus of $${referralBonus} granted to both users`,
        bonusAmount: referralBonus
      };
    } catch (error) {
      console.error('Error in processReferralCode:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get wallet data for any user (admin only)
export const getAdminWalletData = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      // Verify admin role
      const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      const { userId } = data;

      if (!userId) {
        throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
      }

      console.log(`Admin ${context.auth.uid} getting wallet data for user ${userId}`);

      // Get wallet from Firestore
      const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

      if (!walletDoc.exists) {
        // Return empty wallet data if wallet doesn't exist
        return {
          userId,
          balance: 0,
          referralCode: `user${userId.substring(0, 6)}`,
          usedReferral: false,
          history: [],
          grantedBy: 'system',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        };
      }

      return walletDoc.data();
    } catch (error) {
      console.error('Error in getAdminWalletData:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// HTTP endpoint for getting wallet settings (for admin dashboard)
export const getWalletSettingsHttp = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onRequest(async (req, res) => {
    // Handle CORS
    corsHandler(req, res, async () => {
      try {
        // Only allow POST requests
        if (req.method !== 'POST') {
          res.status(405).json({ error: 'Method not allowed' });
          return;
        }

        // Check authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(401).json({ error: 'Unauthorized' });
          return;
        }

        const token = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(token);
        const userId = decodedToken.uid;

        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
          res.status(403).json({ error: 'Unauthorized: Admin access required' });
          return;
        }

        // Get wallet settings
        const settingsDoc = await admin.firestore().collection('adminSettings').doc('walletConfig').get();

        let settings = {
          signupBonus: 0,
          referralBonus: 0,
          enableSignupBonus: false,
          enableReferralBonus: false
        };

        if (settingsDoc.exists) {
          const data = settingsDoc.data();
          settings = {
            signupBonus: data?.signupBonus || 0,
            referralBonus: data?.referralBonus || 0,
            enableSignupBonus: data?.enableSignupBonus || false,
            enableReferralBonus: data?.enableReferralBonus || false
          };
        }

        res.status(200).json({
          success: true,
          settings
        });
      } catch (error) {
        console.error('Error getting wallet settings:', error);
        res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
      }
    });
  });

// HTTP endpoint for configuring wallet settings (for admin dashboard)
export const configureWalletSettings = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onRequest(async (req, res) => {
    // Handle CORS
    corsHandler(req, res, async () => {
      try {
        // Only allow POST requests
        if (req.method !== 'POST') {
          res.status(405).json({ error: 'Method not allowed' });
          return;
        }

        // Check authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(401).json({ error: 'Unauthorized' });
          return;
        }

        const token = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(token);
        const userId = decodedToken.uid;

        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
          res.status(403).json({ error: 'Unauthorized: Admin access required' });
          return;
        }

        // Get settings from request body
        const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = req.body;

        // Validate settings
        if (typeof signupBonus !== 'number' || typeof referralBonus !== 'number' ||
            typeof enableSignupBonus !== 'boolean' || typeof enableReferralBonus !== 'boolean') {
          res.status(400).json({ error: 'Invalid settings format' });
          return;
        }

        // Save settings
        await admin.firestore().collection('adminSettings').doc('walletConfig').set({
          signupBonus,
          referralBonus,
          enableSignupBonus,
          enableReferralBonus,
          updatedAt: admin.firestore.Timestamp.now(),
          updatedBy: userId
        });

        console.log(`Wallet settings updated by admin: ${userId}`);

        res.status(200).json({
          success: true,
          message: 'Wallet settings updated successfully'
        });
      } catch (error) {
        console.error('Error configuring wallet settings:', error);
        res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
      }
    });
  });
